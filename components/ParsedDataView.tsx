'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { FileText, Eye, Code, Copy, Check } from 'lucide-react';

interface ParsedDataViewProps {
  parsedData: {
    success: boolean;
    fileName: string;
    fileSize: number;
    pages: number;
    textLength: number;
    parsedData: any;
    rawText: string;
  };
}

export function ParsedDataView({ parsedData }: ParsedDataViewProps) {
  const [copied, setCopied] = useState(false);

  const handleCopyJSON = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(parsedData.parsedData, null, 2));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const getDocumentTypeDisplay = (docType: string) => {
    const typeMap: { [key: string]: string } = {
      'ARCSYS_INVOICE': 'Arcsys Tax Invoice',
      'HUHTAMAKI_PO': 'Huhtamaki Purchase Order',
      'RESONATE_DELIVERY': 'Resonate Delivery Note',
      'INGRAM_DELIVERY_32': 'Ingram Delivery Challan (32)',
      'INGRAM_INVOICE_29': 'Ingram Tax Invoice (29)',
      'DILIGENT_INVOICE': 'Diligent Solutions Invoice',
      'RESONATE_JOB_ORDER': 'Resonate Job Order',
      'INGRAM_PO': 'Ingram Purchase Order',
      'AIRTEL_PO': 'Airtel Purchase Order',
      'DELIVERY_VOUCHER': 'Delivery Voucher',
      'SALES_VOUCHER': 'Sales Voucher',
      'TAX_INVOICE': 'Tax Invoice',
      'PURCHASE_ORDER': 'Purchase Order',
      'DELIVERY_NOTE': 'Delivery Note',
      'JOB_ORDER': 'Job Order'
    };

    return typeMap[docType] || docType;
  };

  const renderValue = (value: any): React.ReactNode => {
    if (value === null || value === undefined) {
      return <span className="text-gray-400 italic">null</span>;
    }
    
    if (typeof value === 'boolean') {
      return <Badge variant={value ? 'default' : 'secondary'}>{value.toString()}</Badge>;
    }
    
    if (typeof value === 'number') {
      return <span className="text-blue-600 font-mono">{value}</span>;
    }
    
    if (typeof value === 'string') {
      if (value === '') {
        return <span className="text-gray-400 italic">empty</span>;
      }
      return <span className="text-gray-900">{value}</span>;
    }
    
    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="text-gray-400 italic">empty array</span>;
      }
      return (
        <div className="space-y-2">
          {value.map((item, index) => (
            <div key={index} className="border-l-2 border-blue-200 pl-3">
              <div className="text-sm text-gray-600 mb-1">Item {index + 1}</div>
              {typeof item === 'object' ? renderObject(item) : renderValue(item)}
            </div>
          ))}
        </div>
      );
    }
    
    if (typeof value === 'object') {
      return renderObject(value);
    }
    
    return <span>{String(value)}</span>;
  };

  const renderObject = (obj: any) => {
    if (!obj || typeof obj !== 'object') return null;
    
    return (
      <div className="space-y-2">
        {Object.entries(obj).map(([key, value]) => (
          <div key={key} className="flex flex-col space-y-1">
            <div className="text-sm font-medium text-gray-700">{key}:</div>
            <div className="ml-4">{renderValue(value)}</div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            <div>
              <h3 className="font-medium text-gray-900">Parsed Data</h3>
              <p className="text-sm text-gray-600">
                {parsedData.pages} pages • {parsedData.textLength.toLocaleString()} characters
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={parsedData.success ? 'default' : 'destructive'} className="bg-green-100 text-green-800 border-green-200">
              {parsedData.success ? 'Success' : 'Failed'}
            </Badge>
            <Button variant="outline" size="sm" onClick={handleCopyJSON} className="hover:bg-blue-50 hover:border-blue-300">
              {copied ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4 text-blue-600" />}
              {copied ? 'Copied!' : 'Copy JSON'}
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs defaultValue="structured" className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-2 mx-4 mt-4 bg-gray-100">
            <TabsTrigger
              value="structured"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm hover:bg-blue-50"
            >
              <Eye className="h-4 w-4 text-blue-600" />
              Structured View
            </TabsTrigger>
            <TabsTrigger
              value="raw"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm hover:bg-blue-50"
            >
              <Code className="h-4 w-4 text-blue-600" />
              Raw JSON
            </TabsTrigger>
          </TabsList>

          <TabsContent value="structured" className="flex-1 flex flex-col mt-0">
            <div className="sticky top-0 z-10 bg-white border-b border-blue-200 p-4">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                <div className="text-lg flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  Document Type: <span className="text-blue-700 font-bold">{getDocumentTypeDisplay(parsedData.parsedData.document_type || 'Unknown')}</span>
                </div>
              </div>
            </div>
            <div className="flex-1 overflow-auto p-4">
              <div className="space-y-4">
                {parsedData.parsedData && typeof parsedData.parsedData === 'object' ? (
                  Object.entries(parsedData.parsedData).map(([key, value]) => (
                    <div key={key} className="border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
                      <div className="font-medium text-gray-900 mb-3 capitalize text-base">
                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </div>
                      <div className="ml-2">
                        {renderValue(value)}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">No structured data available</p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="raw" className="flex-1 overflow-auto p-4 mt-0">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Raw JSON Data</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-gray-50 p-4 rounded-lg overflow-auto text-sm font-mono whitespace-pre-wrap">
                  {JSON.stringify(parsedData.parsedData, null, 2)}
                </pre>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}