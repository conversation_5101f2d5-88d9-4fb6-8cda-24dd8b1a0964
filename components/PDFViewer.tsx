'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { FileText, ZoomIn, ZoomOut, RotateCw } from 'lucide-react';

interface PDFViewerProps {
  file: File;
}

export function PDFViewer({ file }: PDFViewerProps) {
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [zoom, setZoom] = useState(100);
  const [rotation, setRotation] = useState(0);

  useEffect(() => {
    if (file) {
      const url = URL.createObjectURL(file);
      setPdfUrl(url);

      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [file]);

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 200));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 50));
  };

  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  return (
    <div className="h-full flex flex-col">
      {/* PDF Controls */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            <div>
              <h3 className="font-medium text-gray-900">PDF Viewer</h3>
              <p className="text-sm text-gray-600">
                {file.name} • {(file.size / 1024).toFixed(1)} KB
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleZoomOut} className="hover:bg-blue-50 hover:border-blue-300">
              <ZoomOut className="h-4 w-4 text-blue-600" />
            </Button>
            <span className="text-sm font-medium min-w-[60px] text-center text-blue-700">
              {zoom}%
            </span>
            <Button variant="outline" size="sm" onClick={handleZoomIn} className="hover:bg-blue-50 hover:border-blue-300">
              <ZoomIn className="h-4 w-4 text-blue-600" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleRotate} className="hover:bg-blue-50 hover:border-blue-300">
              <RotateCw className="h-4 w-4 text-blue-600" />
            </Button>
          </div>
        </div>
      </div>

      {/* PDF Content */}
      <div className="flex-1 bg-gray-100 flex justify-center items-center">
        {pdfUrl ? (
          <div className="w-full h-full flex justify-center items-center p-4">
            <iframe
              src={`${pdfUrl}#toolbar=0&navpanes=0&scrollbar=1`}
              className="border border-gray-300 shadow-lg rounded-lg"
              style={{
                width: `${Math.min(zoom, 150)}%`,
                height: '90%',
                maxWidth: '100%',
                maxHeight: '100%',
                transform: `rotate(${rotation}deg)`,
                transformOrigin: 'center center'
              }}
              title="PDF Viewer"
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <FileText className="h-12 w-12 text-blue-400 mx-auto mb-2" />
              <p className="text-blue-600">Loading PDF...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}