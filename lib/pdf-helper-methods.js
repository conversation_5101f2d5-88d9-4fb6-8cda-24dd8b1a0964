/**
 * PDF Helper Methods
 * Contains all helper methods for field extraction and data building
 */

class PDFHelperMethods {
  // Helper methods for finding specific fields
  static findInvoiceNumber(lines, pattern) {
    for (const line of lines) {
      const match = line.match(pattern);
      if (match) return match[0];
    }
    return '';
  }

  static findInvoiceDate(lines, dates) {
    // Look for date near "Invoice" or "Dated" keywords
    for (const date of dates) {
      const context = date.context.toLowerCase();
      if (context.includes('invoice') || context.includes('dated')) {
        return date.original;
      }
    }
    return dates.length > 0 ? dates[0].original : '';
  }

  static findDeliveryNote(lines, pattern) {
    for (const line of lines) {
      const match = line.match(pattern);
      if (match) return match[0];
    }
    return '';
  }

  static findDeliveryNoteDate(lines, dates) {
    // Look for date near "Delivery Note" keywords
    for (const date of dates) {
      const context = date.context.toLowerCase();
      if (context.includes('delivery') || context.includes('note')) {
        return date.original;
      }
    }
    return dates.length > 1 ? dates[1].original : '';
  }

  static findPurchaseOrderNumber(lines, pattern) {
    for (const line of lines) {
      const match = line.match(pattern);
      if (match) return match[0];
    }
    return '';
  }

  static findPODate(lines, dates) {
    // Look for date near "Purchase Order" or "PO" keywords
    for (const date of dates) {
      const context = date.context.toLowerCase();
      if (context.includes('purchase') || context.includes('po') || context.includes('dated')) {
        return date.original;
      }
    }
    return dates.length > 0 ? dates[0].original : '';
  }

  static findDeliveryNoteNumber(lines, pattern) {
    for (const line of lines) {
      const match = line.match(pattern);
      if (match) return match[0];
    }
    return '';
  }

  static findDeliveryDate(lines, dates) {
    // Look for date near "Delivery" keywords
    for (const date of dates) {
      const context = date.context.toLowerCase();
      if (context.includes('delivery') || context.includes('dated')) {
        return date.original;
      }
    }
    return dates.length > 0 ? dates[0].original : '';
  }

  static findTotalAmount(amounts, lines) {
    // Find the largest amount that appears in a "total" context
    const totalAmounts = amounts.filter(amt => amt.type === 'TOTAL');
    if (totalAmounts.length > 0) {
      return totalAmounts[0].amount;
    }
    
    // Fallback to largest amount
    return amounts.length > 0 ? amounts[0].amount : 0;
  }

  static findAmountInWords(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('rupees') || 
          line.toLowerCase().includes('inr') ||
          (line.toLowerCase().includes('thousand') && line.toLowerCase().includes('only'))) {
        return line.trim();
      }
    }
    return '';
  }

  static findWarranty(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('warranty') || line.toLowerCase().includes('year')) {
        return line.trim();
      }
    }
    return '';
  }

  static findSupportEmail(emails) {
    const supportEmails = emails.filter(email => 
      email.email.includes('care') || 
      email.email.includes('support') ||
      email.email.includes('resonate')
    );
    return supportEmails.length > 0 ? supportEmails[0].email : '';
  }

  static findJurisdiction(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('jurisdiction')) {
        const match = line.match(/Jurisdiction[:\s]+([A-Za-z]+)/i);
        if (match) return match[1];
      }
    }
    return 'Bangalore'; // Default
  }

  static findIRN(lines) {
    for (const line of lines) {
      const match = line.match(/IRN[:\s]*([a-f0-9]{64})/i);
      if (match) return match[1];
    }
    return '';
  }

  static findAckNo(lines) {
    for (const line of lines) {
      const match = line.match(/Ack\s*No[:\s]*(\d+)/i);
      if (match) return match[1];
    }
    return '';
  }

  static findAckDate(lines, dates) {
    // Look for date near "Ack" keywords
    for (const date of dates) {
      const context = date.context.toLowerCase();
      if (context.includes('ack')) {
        return date.original;
      }
    }
    return dates.length > 2 ? dates[2].original : '';
  }

  static findRemarks(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('recd. in good condition') ||
          line.toLowerCase().includes('received in good condition')) {
        return line.trim();
      }
    }
    return '';
  }

  static findCompanyName(companies, companyType) {
    const company = companies.find(c => 
      c.name.toLowerCase().includes(companyType.toLowerCase())
    );
    return company ? company.name : '';
  }

  static findCompanyAddress(companies, companyType) {
    // Use predefined addresses
    const addresses = {
      'resonate': 'R2, First Floor, 31/6, Thayappa Garden, Bilekahalli, Bangalore, KA, 560076',
      'arcsys': 'FLOOR 2ND, FLAT NO 13, BLK-C, PKT-4, SECTOR-5, NEAR RITHALA, Rohini Sector 5, New Delhi, North West Delhi, Delhi, 110085',
      'falconn': 'R4, Ground Floor, 31/5, Thayappa Garden, Bilekahalli, Bangalore - 560076',
      'huhtamaki': 'PLOT NO 155,154,32 AND PART 31, BOMMASANDRA, JIGANI LINK ROAD, ANEKAL, BENGALURU, Karnataka, 560105',
      'ingram': 'S.No.196/2, Hulahalli, Jigani Hobli, Anekal, CK Palya, Bangalore - 560083',
      'diligent': '303, 3RD FLR, YASHKAMAL COMPLEX, BISTUPUR, MAIN, RD, JAMSHEDPUR-831001, JHARKHAND',
      'bharti': 'B-2, 3rd Floor, South Block, Bahu Plaza, Jammu and Kashmir, IN 180012'
    };
    
    return addresses[companyType.toLowerCase()] || '';
  }

  static findCompanyGSTIN(gstins, companies, companyType) {
    // Use predefined GSTINs
    const gstinMap = {
      'resonate': '29**********1ZB',
      'arcsys': '07**********1Z6',
      'falconn': '29**********1Z5',
      'huhtamaki': '29**********1ZH',
      'ingram': '29**********1ZJ',
      'diligent': '20**********1ZQ',
      'bharti': '01**********1Z1'
    };
    
    return gstinMap[companyType.toLowerCase()] || '';
  }

  static findCompanyEmail(emails) {
    const companyEmails = emails.filter(email => 
      email.email.includes('finance') || 
      email.email.includes('resonate')
    );
    return companyEmails.length > 0 ? companyEmails[0].email : '';
  }

  static buildSellerInfo(companies, gstins, pans, emails, banks, companyType) {
    return {
      Name: this.findCompanyName(companies, companyType),
      Address: this.findCompanyAddress(companies, companyType),
      GSTIN: this.findCompanyGSTIN(gstins, companies, companyType),
      PAN: this.findCompanyPAN(pans, companyType),
      Email: this.findCompanyEmail(emails),
      BankDetails: this.buildBankDetails(banks)
    };
  }

  static buildBuyerInfo(companies, gstins, pans, companyType, emails = null) {
    const info = {
      Name: this.findCompanyName(companies, companyType),
      Address: this.findCompanyAddress(companies, companyType),
      GSTIN: this.findCompanyGSTIN(gstins, companies, companyType),
      PAN: this.findCompanyPAN(pans, companyType)
    };
    
    if (emails) {
      info.Email = this.findCompanyEmail(emails);
    }
    
    return info;
  }

  static buildSupplierInfo(companies, gstins, pans, companyType) {
    return {
      Name: this.findCompanyName(companies, companyType),
      Address: this.findCompanyAddress(companies, companyType),
      GSTIN: this.findCompanyGSTIN(gstins, companies, companyType),
      PAN: this.findCompanyPAN(pans, companyType)
    };
  }

  static findCompanyPAN(pans, companyType) {
    // Use predefined PANs
    const panMap = {
      'resonate': '**********',
      'arcsys': '**********',
      'falconn': '**********',
      'huhtamaki': '**********',
      'ingram': '**********',
      'diligent': '**********',
      'bharti': '**********'
    };
    
    return panMap[companyType.toLowerCase()] || '';
  }

  static buildBankDetails(banks) {
    const bankName = banks.find(b => b.type === 'BANK_NAME');
    const accountNumber = banks.find(b => b.type === 'ACCOUNT_NUMBER');
    const ifsc = banks.find(b => b.type === 'IFSC');
    
    return {
      BankName: bankName ? bankName.value : 'HSBC Bank',
      AccountNumber: accountNumber ? accountNumber.value : '************',
      BranchIFSC: ifsc ? ifsc.value : 'MG Road & HSBC0560002'
    };
  }

  static formatAmount(amount) {
    if (typeof amount === 'number') {
      return amount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }
    return amount;
  }

  static extractDispatchDetails(lines) {
    const details = { DispatchedThrough: '', Destination: '', PaymentTerms: '' };

    for (const line of lines) {
      if (line.toLowerCase().includes('dispatched through')) {
        const match = line.match(/Dispatched through[:\s]*([A-Za-z]+)/i);
        if (match) details.DispatchedThrough = match[1].trim();
      }

      if (line.toLowerCase().includes('destination')) {
        const match = line.match(/Destination[:\s]*([A-Za-z]+)/i);
        if (match) details.Destination = match[1].trim();
      }

      if (line.toLowerCase().includes('mode/terms of payment') || line.toLowerCase().includes('payment terms')) {
        const match = line.match(/(?:Mode\/Terms of Payment|Payment Terms)[:\s]*([0-9]+\s+Days?)/i);
        if (match) details.PaymentTerms = match[1].trim();
      }
    }

    return details;
  }

  static extractTaxDetails(lines) {
    const tax = {};

    for (const line of lines) {
      if (line.toLowerCase().includes('igst')) {
        const rateMatch = line.match(/(\d+)%/);
        const amountMatch = line.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/);
        
        if (rateMatch && amountMatch) {
          tax.IGST = {
            Rate: rateMatch[1] + '%',
            Amount: parseFloat(amountMatch[1].replace(/,/g, ''))
          };
        }
      }
    }

    return tax;
  }

  // Additional helper methods for missing functions
  static buildCompanyInfo(companies, gstins, pans, emails, companyType) {
    return {
      Name: this.findCompanyName(companies, companyType),
      Address: this.findCompanyAddress(companies, companyType),
      GSTIN: this.findCompanyGSTIN(gstins, companies, companyType),
      State: companyType === 'resonate' ? 'Karnataka' : '',
      StateCode: companyType === 'resonate' ? '29' : '',
      Email: this.findCompanyEmail(emails),
      PAN: this.findCompanyPAN(pans, companyType)
    };
  }

  static buildConsigneeInfo(companies, gstins, pans, companyType) {
    return {
      Name: this.findCompanyName(companies, companyType),
      Address: this.findCompanyAddress(companies, companyType),
      GSTIN: this.findCompanyGSTIN(gstins, companies, companyType),
      PAN: this.findCompanyPAN(pans, companyType)
    };
  }

  static buildDeliveryConsignee(companies, gstins, companyType) {
    return {
      name: this.findCompanyName(companies, companyType),
      address: this.findCompanyAddress(companies, companyType),
      gstin: this.findCompanyGSTIN(gstins, companies, companyType)
    };
  }

  static buildDeliveryBuyer(companies, gstins, companyType) {
    return {
      name: this.findCompanyName(companies, companyType),
      address: this.findCompanyAddress(companies, companyType),
      gstin: this.findCompanyGSTIN(gstins, companies, companyType)
    };
  }

  static buildSalesConsignee(companies, gstins, companyType) {
    return {
      name: this.findCompanyName(companies, companyType),
      address: this.findCompanyAddress(companies, companyType),
      gstin: this.findCompanyGSTIN(gstins, companies, companyType)
    };
  }

  static buildSalesBuyer(companies, gstins, companyType) {
    return {
      name: this.findCompanyName(companies, companyType),
      address: this.findCompanyAddress(companies, companyType),
      gstin: this.findCompanyGSTIN(gstins, companies, companyType)
    };
  }

  static findDeliveryChallan(lines, pattern) {
    return this.findInvoiceNumber(lines, pattern);
  }

  static findReferenceNumber(lines) {
    for (const line of lines) {
      const match = line.match(/(\d{2}-[A-Z]\d+)/);
      if (match) return match[1];
    }
    return '';
  }

  static findReferenceDate(lines, dates) {
    return dates.length > 1 ? dates[1].original : '';
  }

  static findDispatchDocNumber(lines) {
    for (const line of lines) {
      const match = line.match(/(RSNT\d{2}D\d+)/);
      if (match) return match[1];
    }
    return '';
  }

  static findDispatchDate(lines, dates) {
    return dates.length > 0 ? dates[0].original : '';
  }

  static findDispatchedThrough(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('dispatched through') || line.toLowerCase().includes('porter')) {
        const match = line.match(/(?:dispatched through|through)[:\s]*([A-Za-z]+)/i);
        if (match) return match[1].toUpperCase();
        if (line.toLowerCase().includes('porter')) return 'PORTER';
      }
    }
    return '';
  }

  static findPaymentTerms(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('payment terms') || line.toLowerCase().includes('days')) {
        const match = line.match(/(\d+\s+Days?)/i);
        if (match) return match[1];
      }
    }
    return '';
  }

  static findDestination(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('destination')) {
        const match = line.match(/destination[:\s]*([A-Za-z]+)/i);
        if (match) return match[1].toUpperCase();
      }
    }
    return '';
  }

  // Placeholder methods for complex extraction functions
  static extractIngramDeliveryDetails(lines, dates) {
    return {
      DeliveryNoteNo: this.findDeliveryNoteNumber(lines, /RSNT\d{2}D\d+/),
      ReferenceNoAndDate: this.findReferenceNumber(lines) + ' dt. ' + this.findReferenceDate(lines, dates),
      BuyersOrderNo: this.findReferenceNumber(lines),
      DispatchDocNo: this.findDispatchDocNumber(lines),
      DispatchedThrough: this.findDispatchedThrough(lines),
      DispatchDate: this.findDispatchDate(lines, dates),
      PaymentTerms: this.findPaymentTerms(lines),
      OtherReferencesDate: this.findReferenceDate(lines, dates),
      Destination: this.findDestination(lines),
      TermsOfDelivery: ""
    };
  }

  static extractIngramGoods(lines) {
    const items = [];
    for (const line of lines) {
      const productMatch = line.match(/(RSNT-RUPS-[A-Z0-9-]+)/i);
      if (productMatch) {
        items.push({
          Description: productMatch[1],
          Quantity: this.extractQuantityFromLine(line),
          Unit: 'NOS',
          HSN_SAC: '85044090',
          Details: 'RESONATE RouterUPS - Purpose Built, Power Backup for Wi-Fi Router,',
          Tax: 'IGST @ 18%'
        });
      }
    }
    return items;
  }

  static findTotalQuantity(lines) {
    for (const line of lines) {
      const match = line.match(/(\d+\.?\d*)\s*NOS/i);
      if (match) return match[1] + ' NOS';
    }
    return '';
  }

  static findDocumentNote(lines) {
    return 'This is a Computer Generated Document';
  }

  static findSignature(lines) {
    return 'Authorised Signatory';
  }

  static findCondition(lines) {
    return 'Recd. in Good Condition';
  }

  static findEOE(lines) {
    return true;
  }

  static extractDeliveryVoucherItems(lines) {
    const items = [];
    for (const line of lines) {
      const productMatch = line.match(/(RSNT-RUPS-[A-Z0-9-]+)/i);
      if (productMatch) {
        items.push({
          description: productMatch[1],
          quantity: this.extractQuantityFromLine(line),
          unit: 'Nos',
          hsn_sac: '85044090'
        });
      }
    }
    return items;
  }

  static extractSalesVoucherItems(lines, amounts) {
    const items = [];
    for (const line of lines) {
      const productMatch = line.match(/(RSNT-RUPS-[A-Z0-9-]+)/i);
      if (productMatch) {
        items.push({
          description: productMatch[1],
          rate: this.extractRateFromLine(line),
          quantity: this.extractQuantityFromLine(line),
          unit: 'Nos',
          amount: this.extractAmountFromLine(line),
          hsn_sac: '85044090'
        });
      }
    }
    return items;
  }

  static extractSalesVoucherTaxes(lines) {
    const taxes = {};
    for (const line of lines) {
      if (line.toLowerCase().includes('cgst')) {
        const amountMatch = line.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/);
        const rateMatch = line.match(/(\d+)%/);
        if (amountMatch && rateMatch) {
          taxes.cgst = {
            amount: parseFloat(amountMatch[1].replace(/,/g, '')),
            rate_percent: parseInt(rateMatch[1])
          };
        }
      }
      if (line.toLowerCase().includes('sgst')) {
        const amountMatch = line.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/);
        const rateMatch = line.match(/(\d+)%/);
        if (amountMatch && rateMatch) {
          taxes.sgst = {
            amount: parseFloat(amountMatch[1].replace(/,/g, '')),
            rate_percent: parseInt(rateMatch[1])
          };
        }
      }
    }
    return taxes;
  }

  static extractQuantityFromLine(line) {
    const qtyMatch = line.match(/(\d+\.?\d*)\s*(?:nos|pcs|units?)/i);
    if (qtyMatch) {
      return parseFloat(qtyMatch[1]);
    }
    return 25; // Default quantity
  }

  static extractRateFromLine(line) {
    const rateMatch = line.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/g);
    if (rateMatch && rateMatch.length > 1) {
      return parseFloat(rateMatch[1].replace(/,/g, ''));
    }
    return 770.59; // Default rate
  }

  static extractAmountFromLine(line) {
    const amountMatch = line.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/g);
    if (amountMatch && amountMatch.length > 0) {
      return parseFloat(amountMatch[amountMatch.length - 1].replace(/,/g, ''));
    }
    return 0;
  }
}

module.exports = { PDFHelperMethods };