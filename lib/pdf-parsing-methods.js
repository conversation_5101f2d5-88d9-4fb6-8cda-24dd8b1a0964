/**
 * PDF Parsing Methods for 11 Document Types
 * Contains all the specific parsing logic for each document type
 */

class PDFParsingMethods {
  // Enhanced parsing methods for each document type
  static parseArcsysInvoice(context) {
    const { lines, companies, dates, numbers, amounts, gstins, pans, emails, banks } = context;

    return {
      InvoiceNo: this.findInvoiceNumber(lines, /RSNT\d{2}T\d+/),
      InvoiceDate: this.findInvoiceDate(lines, dates),
      DeliveryNote: this.findDeliveryNote(lines, /RSNT\d{2}D\d+/),
      DeliveryNoteDate: this.findDeliveryNoteDate(lines, dates),
      Seller: this.buildSellerInfo(companies, gstins, pans, emails, banks, 'resonate'),
      Buyer: this.buildBuyerInfo(companies, gstins, pans, 'arcsys'),
      DispatchDetails: this.extractDispatchDetails(lines),
      Items: this.extractInvoiceItems(lines),
      Tax: this.extractTaxDetails(lines),
      TotalAmount: this.findTotalAmount(amounts, lines),
      AmountInWords: this.findAmountInWords(lines),
      Warranty: this.findWarranty(lines),
      SupportEmail: this.findSupportEmail(emails),
      Jurisdiction: this.findJurisdiction(lines)
    };
  }

  static parseHuhtamakiPO(context) {
    const { lines, companies, dates, numbers, amounts, gstins, pans, emails } = context;

    return {
      PurchaseOrderNo: this.findPurchaseOrderNumber(lines, /FLCN\d{2}PO\d+/),
      Date: this.findPODate(lines, dates),
      Buyer: this.buildBuyerInfo(companies, gstins, pans, 'falconn', emails),
      Supplier: this.buildSupplierInfo(companies, gstins, pans, 'huhtamaki'),
      Items: this.extractPOItems(lines),
      Taxes: this.extractCGSTSGSTTaxes(lines),
      TotalAmount: this.findTotalAmount(amounts, lines),
      AmountInWords: this.findAmountInWords(lines)
    };
  }

  static parseResonateDelivery(context) {
    const { lines, companies, dates, numbers, gstins, pans, emails } = context;

    const items = this.extractDeliveryItems(lines);
    return {
      DeliveryNoteNo: this.findDeliveryNoteNumber(lines, /RSNT\d{2}J\d+/),
      Date: this.findDeliveryDate(lines, dates),
      Seller: this.buildSellerInfo(companies, gstins, pans, emails, null, 'resonate'),
      Buyer: this.buildBuyerInfo(companies, gstins, pans, 'falconn'),
      Items: items,
      TotalQuantity: items.reduce((sum, item) => sum + (item.Quantity || 0), 0),
      Remarks: this.findRemarks(lines)
    };
  }

  static parseIngramDelivery32(context) {
    const { lines, companies, dates, numbers, gstins, pans, emails } = context;

    return {
      DeliveryChallan: this.findDeliveryChallan(lines, /RSNT\d{2}D\d+/),
      Company: this.buildCompanyInfo(companies, gstins, pans, emails, 'resonate'),
      Consignee: this.buildConsigneeInfo(companies, gstins, pans, 'ingram'),
      Buyer: this.buildBuyerInfo(companies, gstins, pans, 'ingram'),
      DeliveryDetails: this.extractIngramDeliveryDetails(lines, dates),
      Goods: this.extractIngramGoods(lines),
      TotalQuantity: this.findTotalQuantity(lines),
      Jurisdiction: this.findJurisdiction(lines),
      DocumentNote: this.findDocumentNote(lines),
      Signature: this.findSignature(lines),
      Condition: this.findCondition(lines),
      E_O_E: this.findEOE(lines)
    };
  }

  static parseIngramInvoice29(context) {
    const { lines, companies, dates, numbers, amounts, gstins, pans, emails, banks } = context;

    return {
      IRN: this.findIRN(lines),
      AckNo: this.findAckNo(lines),
      AckDate: this.findAckDate(lines, dates),
      Company: this.buildCompanyInfo(companies, gstins, pans, emails, 'resonate'),
      Consignee: this.buildConsigneeInfo(companies, gstins, pans, 'ingram'),
      Buyer: this.buildBuyerInfo(companies, gstins, pans, 'ingram'),
      DeliveryDetails: this.extractIngramInvoiceDetails(lines, dates),
      Goods: this.extractIngramInvoiceGoods(lines, amounts),
      TotalAmount: this.formatAmount(this.findTotalAmount(amounts, lines)),
      TaxDetails: this.extractCGSTSGSTDetails(lines),
      BankDetails: this.buildBankDetails(banks),
      AmountInWords: this.findAmountInWords(lines)
    };
  }

  static parseDiligentInvoice(context) {
    const { lines, companies, dates, numbers, amounts, gstins, pans, emails, banks } = context;

    return {
      IRN: this.findIRN(lines),
      AckNo: this.findAckNo(lines),
      AckDate: this.findAckDate(lines, dates),
      Company: this.buildCompanyInfo(companies, gstins, pans, emails, 'resonate'),
      Consignee: this.buildConsigneeInfo(companies, gstins, pans, 'diligent'),
      Buyer: this.buildBuyerInfo(companies, gstins, pans, 'diligent'),
      DeliveryDetails: this.extractDiligentDeliveryDetails(lines, dates),
      Goods: this.extractDiligentGoods(lines, amounts),
      TotalAmount: this.formatAmount(this.findTotalAmount(amounts, lines)),
      TaxDetails: this.extractIGSTDetails(lines),
      BankDetails: this.buildBankDetails(banks),
      AmountInWords: this.findAmountInWords(lines)
    };
  }

  static parseResonateJobOrder(context) {
    const { lines, companies, dates, numbers, gstins, pans, emails } = context;

    const goods = this.extractJobOrderGoods(lines);
    return {
      JobOrder: this.buildJobOrderCompany(companies, gstins, pans, emails, 'resonate'),
      Consignee: this.buildConsigneeInfo(companies, gstins, pans, 'falconn'),
      Buyer: this.buildBuyerInfo(companies, gstins, pans, 'falconn'),
      DeliveryDetails: this.extractJobOrderDeliveryDetails(lines, dates),
      Goods: goods,
      TotalQuantity: goods.reduce((sum, item) => sum + (item.Quantity || 0), 0),
      Document: this.extractDocumentInfo(lines)
    };
  }

  static parseIngramPO(context) {
    const { lines, companies, dates, numbers, amounts, gstins, pans } = context;

    const items = this.extractIngramPOItems(lines, amounts);
    return {
      PurchaseOrder: this.extractIngramPODetails(lines, dates),
      Buyer: this.buildIngramBuyerInfo(companies, gstins, pans),
      Vendor: this.buildVendorInfo(companies, gstins, 'resonate'),
      Items: items,
      Totals: this.calculateIngramTotals(items),
      Notes: this.extractIngramNotes(lines),
      AuthorizedBy: this.findAuthorizedBy(lines)
    };
  }

  static parseAirtelPO(context) {
    const { lines, companies, dates, numbers, amounts, gstins, pans } = context;

    const items = this.extractAirtelPOItems(lines, amounts);
    return {
      PurchaseOrder: this.extractAirtelPODetails(lines, dates, amounts),
      Buyer: this.buildAirtelBuyerInfo(companies, gstins),
      Vendor: this.buildAirtelVendorInfo(companies, gstins, pans),
      Shipping: this.extractAirtelShipping(lines),
      Items: items,
      Terms: this.extractAirtelTerms(lines),
      Portal_Info: this.extractPortalInfo(lines)
    };
  }

  static parseDeliveryVoucher(context) {
    const { lines, companies, dates, numbers, gstins, emails } = context;

    return {
      document_type: "Delivery Note",
      company: this.findCompanyName(companies, 'resonate'),
      address: this.findCompanyAddress(companies, 'resonate'),
      gstin: this.findCompanyGSTIN(gstins, companies, 'resonate'),
      state: "Karnataka",
      email: this.findCompanyEmail(emails),
      consignee: this.buildDeliveryConsignee(companies, gstins, 'ingram'),
      buyer: this.buildDeliveryBuyer(companies, gstins, 'ingram'),
      delivery_note_no: this.findDeliveryNoteNumber(lines, /RSNT\d{2}D\d+/),
      reference_no: this.findReferenceNumber(lines),
      reference_date: this.findReferenceDate(lines, dates),
      dispatch_doc_no: this.findDispatchDocNumber(lines),
      dispatch_date: this.findDispatchDate(lines, dates),
      dispatched_through: this.findDispatchedThrough(lines),
      payment_terms: this.findPaymentTerms(lines),
      destination: this.findDestination(lines),
      items: this.extractDeliveryVoucherItems(lines)
    };
  }

  static parseSalesVoucher(context) {
    const { lines, companies, dates, numbers, amounts, gstins, emails } = context;

    return {
      document_type: "Tax Invoice",
      company: this.findCompanyName(companies, 'resonate'),
      address: this.findCompanyAddress(companies, 'resonate'),
      gstin: this.findCompanyGSTIN(gstins, companies, 'resonate'),
      state: "Karnataka",
      email: this.findCompanyEmail(emails),
      consignee: this.buildSalesConsignee(companies, gstins, 'ingram'),
      buyer: this.buildSalesBuyer(companies, gstins, 'ingram'),
      invoice_no: this.findInvoiceNumber(lines, /RSNT\d{4}/),
      delivery_note: this.findDeliveryNote(lines, /RSNT\d{2}D\d+/),
      dispatch_doc_no: this.findDispatchDocNumber(lines),
      dispatch_date: this.findDispatchDate(lines, dates),
      payment_terms: this.findPaymentTerms(lines),
      destination: this.findDestination(lines),
      items: this.extractSalesVoucherItems(lines, amounts),
      taxes: this.extractSalesVoucherTaxes(lines),
      total_amount: this.findTotalAmount(amounts, lines),
      amount_in_words: this.findAmountInWords(lines)
    };
  }

  // Helper methods for finding specific fields
  static findInvoiceNumber(lines, pattern) {
    for (const line of lines) {
      const match = line.match(pattern);
      if (match) return match[0];
    }
    return '';
  }

  static findInvoiceDate(lines, dates) {
    // Look for date near "Invoice" or "Dated" keywords
    for (const date of dates) {
      const context = date.context.toLowerCase();
      if (context.includes('invoice') || context.includes('dated')) {
        return date.original;
      }
    }
    return dates.length > 0 ? dates[0].original : '';
  }

  static findDeliveryNote(lines, pattern) {
    for (const line of lines) {
      const match = line.match(pattern);
      if (match) return match[0];
    }
    return '';
  }

  static findDeliveryNoteDate(lines, dates) {
    // Look for date near "Delivery Note" keywords
    for (const date of dates) {
      const context = date.context.toLowerCase();
      if (context.includes('delivery') || context.includes('note')) {
        return date.original;
      }
    }
    return dates.length > 1 ? dates[1].original : '';
  }

  static findTotalAmount(amounts, lines) {
    // Find the largest amount that appears in a "total" context
    const totalAmounts = amounts.filter(amt => amt.type === 'TOTAL');
    if (totalAmounts.length > 0) {
      return totalAmounts[0].amount;
    }
    
    // Fallback to largest amount
    return amounts.length > 0 ? amounts[0].amount : 0;
  }

  static findAmountInWords(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('rupees') || 
          line.toLowerCase().includes('inr') ||
          (line.toLowerCase().includes('thousand') && line.toLowerCase().includes('only'))) {
        return line.trim();
      }
    }
    return '';
  }

  static findWarranty(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('warranty') || line.toLowerCase().includes('year')) {
        return line.trim();
      }
    }
    return '';
  }

  static findSupportEmail(emails) {
    const supportEmails = emails.filter(email => 
      email.email.includes('care') || 
      email.email.includes('support') ||
      email.email.includes('resonate')
    );
    return supportEmails.length > 0 ? supportEmails[0].email : '';
  }

  static findJurisdiction(lines) {
    for (const line of lines) {
      if (line.toLowerCase().includes('jurisdiction')) {
        const match = line.match(/Jurisdiction[:\s]+([A-Za-z]+)/i);
        if (match) return match[1];
      }
    }
    return 'Bangalore'; // Default
  }
}

module.exports = { PDFParsingMethods };
