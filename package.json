{"name": "pdf-invoice-parser", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.536.0", "next": "^14.2.0", "pdf-parse": "^1.1.1", "react": "^18.3.0", "react-dom": "^18.3.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "^14.2.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5"}}